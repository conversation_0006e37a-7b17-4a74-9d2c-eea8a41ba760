{"name": "socialm", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "check-types": "turbo run check-types"}, "devDependencies": {"prettier": "^3.6.2", "turbo": "^2.5.4", "typescript": "5.8.3"}, "engines": {"node": ">=18"}, "packageManager": "yarn@1.22.22", "workspaces": ["apps/*", "packages/*"], "extends": "@repo/typescript-config/react-library.json"}