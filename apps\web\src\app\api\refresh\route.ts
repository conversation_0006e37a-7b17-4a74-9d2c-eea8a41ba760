import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import User from '../../models/users';

import * as jose from 'jose';
import connectDB from '../../lib/mongodb';

export async function GET(request: NextRequest) {
    await connectDB();
    try {
        
        const cookieStore = await cookies();
        const refreshToken = cookieStore.get('refreshToken')?.value as string;
        if(!refreshToken){
            return NextResponse.json({ message: 'Refresh token not found' }, { status: 401 });
        }
        // Decode the refresh token cookie
        const REFRESH_TOKEN_SECRET = new TextEncoder().encode(process.env.REFRESH_TOKEN_SECRET as string);
        const { payload } = await jose.jwtVerify(refreshToken, REFRESH_TOKEN_SECRET);
        if (!payload || !payload.id) {
            return NextResponse.json({ message: 'Invalid token payload' }, { status: 400 });
        }

        // Gret the use from the Decode data
        const user = await User.findById(payload.id);

        // Check that user is exit or not
        if (!user) {
            return NextResponse.json({ message: 'User not found' }, { status: 404 });
        }

        // check that if refresh token is valid or not
        if (user.refreshToken !== refreshToken) {
            return NextResponse.json({ message: 'Refresh Token is revoken' }, { status: 403 });
        }

        // Create new access token Payload
        const newPayload = {
            id: user._id,
            email: user.email,
            username: user.username,
        }

        // create new access token using jose       
        const ACCESS_TOKEN_SECRET = new TextEncoder().encode(process.env.JWT_SECRET as string);         // You can use jwt 
        const newAccesstoken = await new jose.SignJWT(newPayload)
            .setProtectedHeader({ alg: 'HS256' })
            .setExpirationTime('1m')
            .sign(ACCESS_TOKEN_SECRET);

        // Response message
        const response = NextResponse.json({ message: 'Token refreshed' }, { status: 200 });

        // Ser new access token to cookie
        response.cookies.set('token', newAccesstoken, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            path: '/',
            // maxAge: 60 * 60 * 24 * 1, // 1 day
            maxAge: 60 * 1, // 1 day
        });

        return response;
    } catch (error) {
        return NextResponse.json({ message: 'Internal Server Error', error: error }, { status: 500 });
    }
}