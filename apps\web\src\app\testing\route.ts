'use server'
import { NextRequest, NextResponse } from 'next/server';
import connectDB from '../lib/mongodb';
import { headers } from 'next/headers';

export async function GET() {
    try {
        // Test environment variables
        const mongoUri = process.env.MONGO_URI;
        const jwtSecret = process.env.JWT_SECRET;

        console.log("Environment check:");
        console.log("MONGO_URI exists:", !!mongoUri);
        console.log("JWT_SECRET exists:", !!jwtSecret);

        // Test database connection
        await connectDB();


        const headerList =await headers();
        const userId = headerList.get('x-user-id');

        return NextResponse.json({
            message: "Debug info",
            env: {
                mongoUri: mongoUri ? "✅ Set" : "❌ Missing",
                jwtSecret: jwtSecret ? "✅ Set" : "❌ Missing",
                anotheThing: "That is this is testing"
            },
            database: "✅ Connected",
            userId: userId,
        });

    } catch (error) {
        console.error("Debug error:", error);
        return NextResponse.json({
            error: "Debug failed",
            details: error instanceof Error ? error.message : "Unknown error"
        }, { status: 500 });
    }
}

export async function POST(request: NextRequest) {
    try {
        const body = await request.json();
        console.log("Received POST data:", body);

        return NextResponse.json({
            message: "POST request received successfully",
            receivedData: body
        });

    } catch (error) {
        console.error("POST debug error:", error);
        return NextResponse.json({
            error: "POST debug failed",
            details: error instanceof Error ? error.message : "Unknown error"
        }, { status: 500 });
    }
}

