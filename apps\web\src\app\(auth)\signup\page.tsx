'use client'

import React, { useState } from 'react'
import { string, z } from 'zod'
import { Alert, AlertDescription, AlertTitle } from "@repo/ui/components/alert"
import { useRouter } from 'next/navigation';

const userSchema = z.object({
  username: z.string().min(3, "Username must be at least 3 characters long"),
  email: z.string().email("Invalid email address"),
  password: z.string().min(3, "Password must be at least 8 characters long"),
  name: z.string()
});

function SignupPage() {
  const [errors, setErrors] = useState("");
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter();
  const [char, Setchar] = useState("")

  async function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();                                   // This help from reloading the webapp
    const form = new FormData(e.currentTarget)            // When you gave e.currentTarget 👆 make sure you did this => <HTMLFormElement> 
    const formData = Object.fromEntries(form);            // This line extreact the data from the form

    try {
      const validateData = userSchema.parse(formData);
      console.log(validateData);
      setErrors("")
      const response = await fetch('/api/auth/signup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      })

      const data = await response.json();

      if (response.ok) {
        setMessage(data.message || "Signup Successfull");
        console.log("Signup Successfull: ", data);
        router.push('/dashboard');
        
      } else {
        setErrors(data.error || "Signup failed ")
        console.error("Signup failed: ", data);
      }

    } catch (error) {

      if (error instanceof z.ZodError) {
        const errorObj = {};
        error.errors.forEach((err) => {
          errorObj[err.path[0]] = error.message;
        })
        // setErrors(formattedErrors);
        console.log(errorObj)
      }

      console.error(error);

    }
  }

  function handleChange(e: React.ChangeEvent<HTMLInputElement>){
    const {name, value } = e.target
    console.log(name, value)
    Setchar(value)
  }
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-900 p-8">
      <div className="bg-gray-800 border-2 border-gray-600 shadow-lg rounded-none p-8 w-full max-w-md">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-white mb-2">Signup</h2>
          <p className="text-gray-400 text-sm">Create your account</p>
        </div>
        <form className="w-full flex flex-col gap-6" onSubmit={handleSubmit}>
          {/* Error Message */}
          {errors && (
            <div>
              <div className="mb-4 p-3 bg-red-900 border border-red-700 rounded text-red-200 text-sm">
                {errors}
              </div>
            </div>
          )}

          {/* Success Message */}
          {message && (
            <div className="mb-4 p-3 bg-green-900 border border-green-700 rounded text-green-200 text-sm">
              {message}
            </div>
          )}

          <div className="flex flex-col gap-1">
            <label htmlFor="name" className="text-sm font-medium text-white mb-2">Name</label>
            <input id="name" name="name" type="text" placeholder="Your Name" className="p-3 bg-gray-700 border-2 border-gray-600 rounded-none text-white placeholder-gray-400 focus:outline-none focus:border-gray-400 transition-colors" />
          </div>

          <div className="flex flex-col gap-1">
            <label htmlFor="username" className="text-sm font-medium text-white mb-2">Username</label>
            <input onChange={handleChange} id="username" name="username" type="text" placeholder="Username" className="p-3 bg-gray-700 border-2 border-gray-600 rounded-none text-white placeholder-gray-400 focus:outline-none focus:border-gray-400 transition-colors" />
          </div>

          <div className="flex flex-col gap-1">
            <label htmlFor="email" className="text-sm font-medium text-white mb-2">Email</label>
            <input id="email" name="email" type="email" placeholder="<EMAIL>" className="p-3 bg-gray-700 border-2 border-gray-600 rounded-none text-white placeholder-gray-400 focus:outline-none focus:border-gray-400 transition-colors" />
          </div>
          
          <div className="flex flex-col gap-1">
            <label htmlFor="password" className="text-sm font-medium text-white mb-2">Password</label>
            <input id="password" name="password" type="password" placeholder="••••••••" className="p-3 bg-gray-700 border-2 border-gray-600 rounded-none text-white placeholder-gray-400 focus:outline-none focus:border-gray-400 transition-colors" />
          </div>

          <button type="submit" className="mt-2 w-full bg-white text-black font-semibold py-3 rounded-none border-2 border-white hover:bg-gray-800 hover:text-white transition-all duration-200 focus:outline-none">Signup</button>
        
        </form>
        <p className="mt-8 text-center text-sm text-gray-400">Already have an account? <a href="/login" className="text-white font-medium underline hover:text-gray-300 transition-colors">Login</a></p>
      </div>
    </div>
  )
}

export default SignupPage