import { NextRequest, NextResponse } from 'next/server';
import { cookies, headers } from 'next/headers';
import User from '../../../models/users';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import connectDB from '../../../lib/mongodb';

connectDB();


export async function POST(request: NextRequest) {
    try {
        const body = await request.json();
        const { email, username, password, name } = body;
        console.log(body);

        if (!email || !password || !name || !username) {
            return NextResponse.json({ error: 'Missing fields' }, { status: 400 });
        }

        const existingUser = await User.findOne({ $or: [{ email }, { username }] });
        if (existingUser) {
            if (existingUser.email === email) {
                return NextResponse.json({ error: 'Email already exists' }, { status: 409 });
            }
            if (existingUser.username === username) {
                return NextResponse.json({ error: "Username already exists" }, { status: 409 })
            }
        }

        const hashedPassword = bcrypt.hashSync(password, 10);

        const newUser = new User({
            name,
            username,
            email,
            password: hashedPassword,
        });

        await newUser.save();

        const token = jwt.sign({ id: newUser._id }, process.env.JWT_SECRET as string, { expiresIn: '30d' });

        const response = NextResponse.json({ message: "Registration Successfull" }, { status: 201 });

        response.cookies.set('token', token, {
            httpOnly: true,
            maxAge: 60 * 60 * 24 * 30, // 30 days
        })
        return response;
    } catch (err: any) {
        console.error(err);
        if (err.code === 11000) {
            const field = Object.keys(err.keyValue)[0];
            const message = `An account with this ${field} already exists.`;
            return NextResponse.json({ error: message }, { status: 409 });
        }
        return NextResponse.json({ error: 'An internal server error occurred.' }, { status: 500 });
    }
}
