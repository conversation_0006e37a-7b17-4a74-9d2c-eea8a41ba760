import mongoose from "mongoose";

const MONGO_URI = process.env.MONGO_URI;

type ConnectionObj = {
    isConnected?: number,
}
const connection: ConnectionObj = {};

export default async function connectDB() {
    if (connection.isConnected) {
        console.log("Database is connected");
        return;
    } else {
        try {
            const mongodb = await mongoose.connect(process.env.MONGO_URI || '');
            connection.isConnected = mongodb.connection.readyState
            console.log("Connection is established");
        }
        catch {
            console.log("Database connection is failed");
        }
    }
}