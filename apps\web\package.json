{"name": "web", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack --port 3000", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0", "check-types": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-slot": "^1.2.3", "@repo/ui": "*", "@tailwindcss/postcss": "^4.1.11", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "mongoose": "^8.16.3", "next": "^15.3.0", "next-themes": "^0.4.6", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.5", "vaul": "^1.1.2", "zod": "^4.0.5"}, "devDependencies": {"@repo/eslint-config": "*", "@repo/typescript-config": "*", "@types/node": "^22.15.3", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "eslint": "^9.30.0", "typescript": "5.8.2"}}