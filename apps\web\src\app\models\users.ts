import mongoose, { Document, Model } from 'mongoose'

export interface user extends Document{
    name : string,
    email : string,
    username : string,
    password : string,
    refreshToken : string,
    createdAt : Date,
    updatedAt : Date
}

const userSchema = new mongoose.Schema<user>({
    name : {type: String, required: true},
    username : {type: String, required: true, unique: true},
    email : {type: String, required: true, unique: true},
    password : {type: String, required: true},
    refreshToken : {type: String, required: false},
    createdAt : {type: Date, required: false},
    updatedAt : {type: Date, required: false}
}, {
    timestamps: true // This will add createdAt and updatedAt fields
})

const User: Model<user> = mongoose.models.User || mongoose.model<user>('User', userSchema);

export default User;