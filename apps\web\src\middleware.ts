import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import * as jose from 'jose'

export async function middleware(request: NextRequest) {
    console.log("Middleware called for", request.url);
    const LOGIN_URI = new URL('/login', request.url)
    const cookieStore = await cookies();
    const token = cookieStore.get('token')?.value;
    if (!token) {
        return NextResponse.redirect(LOGIN_URI);
    }

    try {
        const secret = new TextEncoder().encode(process.env.JWT_SECRET as string);
        const { payload } = await jose.jwtVerify(token, secret);

        if (!payload) {
            console.log("Token is expired")
            return NextResponse.redirect(LOGIN_URI)
        }
        const requestHeaders = new Headers(request.headers);
        requestHeaders.set("x-user-id", payload.id as string);
 
        // Pass the new headers to the next middleware or page
        return NextResponse.next({
            request: {
                headers: requestHeaders,
            },
        });

    } catch (error) {
        console.error("Error processing token in middleware:", error);
        return NextResponse.redirect(LOGIN_URI);
    }
}

export const config = {
    matcher: [
        /*
         * Match all request paths except for the ones starting with:
         * - api (API routes)
         * - _next/static (static files)
         * - _next/image (image optimization files)
         * - favicon.ico (favicon file)
         * - login (the login page itself)
         * - signup (the signup page)
         */
        '/((?!api|_next/static|_next/image|favicon.ico|login|signup).*)'
    ],
}