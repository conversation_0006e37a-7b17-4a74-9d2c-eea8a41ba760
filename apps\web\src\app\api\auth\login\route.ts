// app/api/auth/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { cookies, headers } from 'next/headers';
import User from '../../../models/users';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import connectDB from '../../../lib/mongodb';


export async function POST(request: NextRequest) {
    try {
        await connectDB();

        // Parse request body
        const body = await request.json();
        const { email, password } = body;

        // Validate input
        if (!email || !password) {
            return NextResponse.json({
                error: "Email and password are required"
            }, { status: 400 });
        }

        // Find the user
        const user = await User.findOne({ email });
        if (!user) {
            console.log("User not found for email:", email);
            return NextResponse.json({
                error: "User not found"
            }, { status: 404 });
        }

        // Compare the password
        const passwordMatch = await bcrypt.compare(password, user.password);

        // Create JWT token
        if (passwordMatch) {

            // Access token Payload
            const accessTokenPayload = {
                id: user._id,
                email: user.email,
                name: user.name,
            };

            // Refresh token payload
            const refreshTeokenPayload = { id: user._id }

            // Generate the accesstoken and refresh token with differenct expire time
            const newAccessToken = jwt.sign(accessTokenPayload, process.env.JWT_SECRET as string, {
                expiresIn: '1m',
            });

            const newRefreshToken = jwt.sign(refreshTeokenPayload, process.env.REFRESH_TOKEN_SECRET as string, {
                expiresIn: "10m"
            });

            user.refreshToken = newRefreshToken;
            await user.save();

            // Create success response
            const response = NextResponse.json({ message: 'Login successful' }, { status: 200 });

            //const response = NextResponse.redirect(new URL('/dashboard', request.url));

            // Set cookie (uncomment if you want to use cookies)
            response.cookies.set('token', newAccessToken, {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                path: '/',
                //maxAge: 60 * 60 * 24 * 1, // 1 days
                maxAge: 60 * 1, // 1m
            });
            response.cookies.set('refreshToken', newRefreshToken, {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                path: '/',
                //maxAge: 60 * 60 * 24 * 10, // 10 days
                maxAge: 60 * 10, // 5 m
            });

            console.log("Login successful for email:", email);

            return response;
        } else {
            console.log("Invalid password for email:", email);
            return NextResponse.json({
                error: "Invalid credentials"
            }, { status: 401 });
        }

    } catch (error) {
        console.error("Login API Error:", error);
        return NextResponse.json({
            error: 'An internal server error occurred'
        }, { status: 500 });
    }
}